import "./globals.css";
import "./theme-config.css";
import "@radix-ui/themes/styles.css";
import { Inter } from "next/font/google";
import { Theme } from "@radix-ui/themes";

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "Tailwind + Radix Test",
  description: "Minimal working setup",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={`${inter.className} min-h-screen`}>
        {/* Radix theme */}
        <Theme>
          <main className="min-h-screen flex items-center justify-center">
            {children}
          </main>
        </Theme>
      </body>
    </html>
  );
}
