"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React from "react";
import { AiFillBug } from "react-icons/ai";
import styles from "./styles/Navbar.module.css";

const NavBar = () => {
  const currentPath = usePathname();

  const links = [
    { label: "Home", href: "/" },
    { label: "About", href: "/about" },
    { label: "Contact", href: "/contact" },
    { label: "Dashboard", href: "/dashboard" },
    { label: "Issues", href: "/issues" },
  ];

  return (
    <nav className={styles.navbar}>
      <Link href="/">
        <AiFillBug />
      </Link>
      <ul className={styles.navLinks}>
        {links.map((link) => (
          <Link
            key={link.href}
            className={`${styles.link} ${
              currentPath === link.href ? styles.active : ""
            }`}
            href={link.href}
          >
            {link.label}
          </Link>
        ))}
      </ul>
    </nav>
  );
};

export default NavBar;
