{"name": "issue-tracker", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.15.0", "@radix-ui/themes": "^3.2.1", "@types/node": "24.3.0", "@types/react": "19.1.11", "@types/react-dom": "19.1.7", "axios": "^1.11.0", "classnames": "^2.3.2", "easymde": "^2.20.0", "eslint": "9.34.0", "eslint-config-next": "15.5.0", "next": "15.5.0", "react": "19.1.1", "react-dom": "19.1.1", "react-hook-form": "^7.62.0", "react-icons": "^4.11.0", "react-simplemde-editor": "^5.2.0", "typescript": "5.9.2", "zod": "^4.1.5"}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "prisma": "^6.15.0", "tailwindcss": "^4.1.13"}}