import { NextRequest, NextResponse } from "next/server";
import Prisma from "@prisma/client";
import { PrismaClient } from "@prisma/client";
import { z } from "zod";
import { prisma } from "@app/generated/prisma";
import { createIssueSchema } from "../../validationSchema";

const createIssueSchema = z.object({
    title: z.string().min(1).max(255),
    description: z.string().min(1).max(255).optional(),
    status: z.enum(["OPEN", "IN_PROGRESS", "CLOSED"]),
})

export async function POST(request: NextRequest) {
    const body = await request.json();
    const validation = createIssueSchema.safeParse(body);
    if (!validation.success) {
        return NextResponse.json(validation.error.errors, { status: 400 });

        const newIssue = await prisma.issue.create({
            data: {
                title: body.title,
                description: body.description,
                status: body.status,
            },
        });
        return NextResponse.json(newIssue, { status: 201 });

    }
}