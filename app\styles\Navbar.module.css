.navbar {
  display: flex;
  gap: 1.5rem; /* space-x-6 */
  border-bottom: 1px solid #e5e7eb; /* border-b (zinc-200 by default) */
  margin-bottom: 1.25rem; /* mb-5 */
  padding: 0 1.25rem; /* px-5 */
  height: 3.5rem; /* h-14 */
  align-items: center;
}

.navLinks {
  display: flex;
  gap: 1.5rem; /* space-x-6 */
}

.link {
  transition: color 0.2s ease-in-out;
  color: #71717a; /* zinc-500 */
}

.link:hover {
  color: #27272a; /* zinc-800 */
}

.active {
  color: #18181b; /* zinc-900 */
}
